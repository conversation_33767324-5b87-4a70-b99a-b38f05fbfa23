@import url("https://fonts.googleapis.com/css2?family=Montserrat:wght@100;200;300;400;500;600;700;800;900&family=Nunito:wght@200;300;400;500;600;700;800;900;1000&display=swap");
* {
  box-sizing: border-box;
  margin: 0px;
  padding: 0px;
  font-family: "Nunito", sans-serif;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--primary-pink-lightest);
}



.montserrat {
  font-family: "Montserrat", sans-serif;
}

img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}

.container {
  max-width: 1300px;
  margin: 0px auto 0px auto;
  padding: 0px 40px;
}
@media (min-width: 1200px) and (max-width: 1441px) {
  .container {
    max-width: 1250px;
    padding: 0px 36px;
  }
}
@media (max-width: 767px) {
  .container {
    padding: 0px 30px;
  }
}
@media (max-width: 479px) {
  .container {
    padding: 0px 30px 0px 20px;
  }
}

.about-me {
  padding-top: 50px;
  padding-bottom: 50px;

}
.about-me .about-me-container {
  position: relative;
}
@media (max-width: 960px) {
  .about-me .about-me-container {
    padding-bottom: 100px;
  }
}
.about-me .about-me-container .about-me-title {
  font-size: 55px;
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: 20px;
}
@media (max-width: 500px) {
  .about-me .about-me-container .about-me-title {
    font-size: 30px;
  }
}

.about-me-flex-container {
  margin-top: -25px;
  margin-left: clamp(20px, 10vw, 150px); /* Responsive margin that won't cause overflow */
  left: clamp(10px, 5vw, 100px);         /* Responsive positioning */
  display: flex;
  justify-content: space-between;
  max-width: calc(100vw - 40px);         /* Ensure container doesn't exceed viewport */
}
@media (max-width: 960px) {
  .about-me-flex-container {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-left: 0px;
    gap: 50px;
  }
}
@media (max-width: 500px) {
  .about-me-flex-container {
    margin-top: -10px;
  }
}
.about-me-flex-container .about-me-image {
  position: relative;
  width: 400px;
  height: 400px;
}
@media (max-width: 500px) {
  .about-me-flex-container .about-me-image {
    width: 300px;
    height: 300px;
  }
}
.about-me-flex-container .about-me-image .back-div {
  position: absolute;
  bottom: 0;
  z-index: -3;
  background-color: var(--accent-pink);
  width: 80%;
  height: 80%;
}
.about-me-title{
  font-family: "Pacifico", cursive;
}
.about-me-flex-container .about-me-image .black-image {
  z-index: -2;
  position: absolute;
  left: 10px;
  bottom: 10px;
  height: 100%;
}
.about-me-flex-container .about-me-image .black-image img {
  height: 100%;
}
.about-me-flex-container .about-me-image .main-image {
  width: 75%;
  height: 75%;
  overflow: hidden;
  position: absolute;
  right: 15%;
  top: 15%;
  box-shadow: var(--primary-pink-light) 0px 7px 50px 0px;
  transition: all 0.2s ease-out;
}
.about-me-flex-container .about-me-image .main-image:hover {
  transform-origin: top center;
  transform: scale(1.5);
  border-radius: 25px;
}
.about-me-flex-container .about-me-image .main-image img {
  transform-origin: center center;
  transform: scale(2);
  -o-object-fit: cover;
     object-fit: cover;
  transition: all 0.2s ease-out;
}
.about-me-flex-container .about-me-image .main-image img:hover {
  transform: scale(1);
}
.about-me-flex-container .about-me-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 50px;
  flex: 0 0 40%;
}
@media (max-width: 960px) {
  .about-me-flex-container .about-me-content {
    flex-direction: row-reverse;
  }
}
.about-me-flex-container .about-me-content .logo {
  max-width: 200px;
}
.about-me-flex-container .about-me-content .logo img {
  filter: drop-shadow(0 0 25px rgb(0, 0, 0));
}
@media (max-width: 500px) {
  .about-me-flex-container .about-me-content .logo img {
    transform: rotateZ(90deg);
  }
}
.about-me-flex-container .about-me-content .text {
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 18px;
}
@media (max-width: 500px) {
  .about-me-flex-container .about-me-content .text {
    font-size: 16px;
  }
}




.cta:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  display: block;
  border-radius: 50px;
  background: var(--primary-pink-lighter);
  width: 45px;
  height: 45px;
  transition: all 0.3s ease;
}

.cta span {
  position: relative;
  font-family: "Ubuntu", sans-serif;
  font-size: 18px;
  font-weight: 700;
  letter-spacing: 0.05em;
  color: var(--text-secondary);
}

.cta svg {
  position: relative;
  top: 0;
  margin-left: 10px;
  fill: none;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke: var(--text-secondary);
  stroke-width: 2;
  transform: translateX(-5px);
  transition: all 0.3s ease;
}

.cta:hover:before {
  width: 100%;
  background: var(--primary-pink-lighter);
}

.cta:hover svg {
  transform: translateX(0);
}

.cta:active {
  transform: scale(0.95);
}
.about-me-flex-container {
  display: flex;
  gap: 20px;
}

.about-me-image {
  position: relative;
}

.back-image img {
  width: 300px;
  height: auto;
  filter: blur(10px);
  transition: filter 0.5s ease-in-out, transform 0.5s ease-in-out;
}

.front-image img {
  width: 300px;
  height: auto;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
}

.about-me-image .show-front-image img {
  filter: blur(0);
  transform: scale(1.05); /* Slight zoom when clicked */
}

.cta {
  position: relative;
  display: inline-block;
  padding: 12px 24px;
  background-color: #f7f7f7;
  border-radius: 30px;
  cursor: pointer;
  transition: background-color 0.3s ease-in-out;
}

.cta:hover {
  background-color: #e2e2e2;
}

.text {
  margin-top: 20px;
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-secondary);
}
